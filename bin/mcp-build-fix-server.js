#!/usr/bin/env node

/**
 * MCP BuildFix Server 启动脚本
 * 
 * 使用方法:
 * node bin/mcp-build-fix-server.js [options]
 * 
 * 选项:
 * --verbose, -v    启用详细日志
 * --help, -h       显示帮助信息
 */

const { Command } = require('commander');
const chalk = require('chalk');
const BuildFixMcpServer = require('../src/mcp/BuildFixMcpServer');

const program = new Command();

program
  .name('mcp-build-fix-server')
  .description('MCP Server for BuildFixAgent - AI-powered build error fixing')
  .version('1.0.0')
  .option('-v, --verbose', 'Enable verbose logging')
  .option('--debug', 'Enable debug mode')
  .helpOption('-h, --help', 'Display help information');

program.parse();

const options = program.opts();

async function main() {
  try {
    if (options.verbose) {
      console.log(chalk.blue('🚀 Starting BuildFix MCP Server...'));
      console.log(chalk.gray('Options:'), options);
    }

    // 创建并启动 MCP 服务器
    const server = new BuildFixMcpServer({
      verbose: options.verbose,
      debug: options.debug
    });

    // 处理进程信号
    process.on('SIGINT', () => {
      if (options.verbose) {
        console.log(chalk.yellow('\n📡 Shutting down MCP server...'));
      }
      process.exit(0);
    });

    process.on('SIGTERM', () => {
      if (options.verbose) {
        console.log(chalk.yellow('\n📡 Shutting down MCP server...'));
      }
      process.exit(0);
    });

    // 启动服务器
    await server.start();

  } catch (error) {
    console.error(chalk.red('❌ Failed to start MCP server:'), error.message);
    if (options.verbose) {
      console.error(error.stack);
    }
    process.exit(1);
  }
}

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  console.error(chalk.red('❌ Uncaught Exception:'), error.message);
  if (options.verbose) {
    console.error(error.stack);
  }
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error(chalk.red('❌ Unhandled Rejection at:'), promise, 'reason:', reason);
  process.exit(1);
});

main();
