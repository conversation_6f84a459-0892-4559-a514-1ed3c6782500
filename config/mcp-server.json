{"mcpServers": {"build-fix-agent": {"command": "node", "args": ["bin/mcp-build-fix-server.js"], "cwd": ".", "env": {"NODE_ENV": "production"}, "description": "AI-powered build error fixing agent for Vue 2->3 migration", "capabilities": {"tools": [{"name": "fix-build-errors", "description": "Automatically fix build errors in a project", "parameters": {"projectPath": "string", "buildOutput": "string", "maxAttempts": "number (optional, default: 3)"}}, {"name": "fix-runtime-error", "description": "Fix runtime errors in Vue components", "parameters": {"projectPath": "string", "fileName": "string", "errorMessage": "string", "stack": "string (optional)"}}], "resources": [{"uri": "fix-stats://current", "description": "Current session fix statistics"}, {"uri": "session-history://latest", "description": "Latest session history and logs"}, {"uri": "fix-logs://{sessionId}", "description": "Detailed logs for a specific session"}]}, "metadata": {"version": "1.0.0", "author": "Galaxy Transit Team", "license": "MIT", "repository": "https://github.com/galaxy/galaxy-transit", "tags": ["vue", "migration", "build-fix", "ai", "automation"]}}}, "defaults": {"timeout": 30000, "retries": 3, "logLevel": "info"}, "examples": {"fix-build-errors": {"description": "Fix build errors in a Vue project", "request": {"tool": "fix-build-errors", "arguments": {"projectPath": "/path/to/vue-project", "buildOutput": "ERROR in ./src/components/HelloWorld.vue\nModule not found: Error: Can't resolve 'vue' in '/path/to/vue-project/src/components'", "maxAttempts": 3}}}, "fix-runtime-error": {"description": "Fix a runtime error in a Vue component", "request": {"tool": "fix-runtime-error", "arguments": {"projectPath": "/path/to/vue-project", "fileName": "src/components/HelloWorld.vue", "errorMessage": "Cannot read property 'xxx' of undefined", "stack": "at HelloWorld.vue:25:10"}}}, "get-fix-stats": {"description": "Get current session statistics", "request": {"resource": "fix-stats://current"}}, "get-session-history": {"description": "Get session history", "request": {"resource": "session-history://latest"}}}}