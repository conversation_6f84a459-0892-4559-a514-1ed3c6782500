const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const ora = require('ora');

const PackageJsonMigrator = require('./PackageJsonMigrator');
const SassMigrator = require('./SassMigrator');
const VueMigrator = require('./VueMigrator');
const BuildFixer = require('./BuildFixer');
const DevRunFixer = require('../domain/dev-run-fix/DevRunFixer');
const RuntimePageChecker = require('../../bin/page-validator');
const SmartVueConfigMerge = require('./SmartVueConfigMerge');

/**
 * AutoMigrator - Vue 2 到 Vue 3 全自动迁移工具
 *
 * 功能：
 * 1. 复制 vue.config.js 到目标目录
 * 2. 执行 package.json 更新
 * 3. 执行 Sass 迁移
 * 4. 执行 Vue 代码迁移
 * 5. 安装依赖包
 * 6. 执行构建修复
 * 7. 如果构建成功，启动开发服务器
 * 8. 如果开发服务器启动成功，执行页面验证
 */
class AutoMigrator {
  constructor(projectPath, targetProjectPath = null, options = {}) {
    this.projectPath = path.resolve(projectPath);
    this.targetProjectPath = targetProjectPath ? path.resolve(targetProjectPath) : this.projectPath;
    this.inPlaceMode = !targetProjectPath || targetProjectPath === projectPath;
    this.options = {
      verbose: false,
      dryRun: false,
      skipSteps: [], // 可以跳过的步骤: ['config', 'package', 'sass', 'vue', 'install', 'build', 'devserver', 'validate']
      buildFixerOptions: {},
      devServerOptions: {},
      pageValidatorOptions: {},
      ...options
    };

    this.stats = {
      startTime: Date.now(),
      endTime: null,
      success: false,
      errors: [],
      stepResults: {
        configCopy: null,
        packageMigration: null,
        sassMigration: null,
        vueMigration: null,
        dependencyInstall: null,
        buildFix: null,
        devServerStart: null,
        pageValidation: null
      }
    };

    this.spinner = null;
  }

  /**
   * 记录日志
   */
  log(message, ...args) {
    if (this.options.verbose) {
      console.log(message, ...args);
    }
  }

  /**
   * 步骤 1: 智能合并 vue.config.js 配置文件
   */
  async copyVueConfig() {
    if (this.options.skipSteps.includes('config')) {
      this.log(chalk.yellow('⏭️  跳过配置文件合并步骤'));
      return { success: true, skipped: true };
    }

    this.spinner = ora('步骤 1/8: 智能合并 vue.config.js 配置文件，删除 babel.config.js ...').start();

    try {
      const smartMerger = new SmartVueConfigMerge({
        verbose: this.options.verbose,
        dryRun: this.options.dryRun
      });

      // 删除 babel.config.js（Vue 2 特有）
      const babelRemoved = await smartMerger.removeBabelConfig(this.targetProjectPath);
      if (babelRemoved) {
        this.log(chalk.gray(`   删除 Vue 2 的 babel.config.js`));
      }

      // 删除 package-lock.json（如果存在）
      const packageLockPath = path.join(this.targetProjectPath, 'package-lock.json');
      if (await fs.pathExists(packageLockPath)) {
        await fs.remove(packageLockPath);
        this.log(chalk.gray(`   删除 package-lock.json`));
      }

      // 智能合并配置文件
      const mergeResult = await smartMerger.mergeVueConfig(
        this.projectPath,  // 老项目路径
        this.targetProjectPath  // 目标路径
      );

      // 更新 spinner 消息
      if (mergeResult.aiUsed) {
        this.spinner.succeed('✅ vue.config.js 配置文件 AI 智能合并完成');
        this.log(chalk.green(`   🤖 使用 AI 智能合并了老项目配置`));
      } else {
        this.spinner.succeed('✅ vue.config.js 配置文件处理完成');
        this.log(chalk.yellow(`   📋 ${mergeResult.message}`));
      }

      // 记录 AI 统计信息
      if (mergeResult.aiUsed) {
        const aiStats = smartMerger.getStats();
        this.log(chalk.gray(`   AI 调用统计: 成功 ${aiStats.success}, 失败 ${aiStats.failed}`));
      }

      return {
        success: true,
        action: mergeResult.action,
        aiUsed: mergeResult.aiUsed,
        message: mergeResult.message,
        targetConfigPath: mergeResult.targetConfigPath,
        babelRemoved
      };

    } catch (error) {
      this.spinner.fail('❌ vue.config.js 配置文件处理失败');
      const errorMsg = `配置文件处理失败: ${error.message}`;
      this.stats.errors.push(errorMsg);
      throw new Error(errorMsg);
    }
  }

  /**
   * 步骤 2: 执行 package.json 更新
   */
  async migratePackageJson() {
    if (this.options.skipSteps.includes('package')) {
      this.log(chalk.yellow('⏭️  跳过 package.json 迁移步骤'));
      return { success: true, skipped: true };
    }

    this.spinner = ora('步骤 2/8: 更新 package.json 依赖...').start();

    try {
      const migrator = new PackageJsonMigrator({
        sourceProjectPath: this.projectPath,
        targetProjectPath: this.targetProjectPath,
        workingPath: this.targetProjectPath,
        migrationMode: true,
        verbose: this.options.verbose,
        dryRun: this.options.dryRun
      });

      const result = await migrator.processPackageJson();

      this.spinner.succeed('✅ package.json 依赖更新完成');

      return {
        success: true,
        ...result,
        message: 'package.json 依赖更新成功'
      };
    } catch (error) {
      this.spinner.fail('❌ package.json 依赖更新失败');
      const errorMsg = `package.json 迁移失败: ${error.message}`;
      this.stats.errors.push(errorMsg);
      throw new Error(errorMsg);
    }
  }

  /**
   * 步骤 3: 执行 Sass 迁移
   */
  async migrateSass() {
    if (this.options.skipSteps.includes('sass')) {
      this.log(chalk.yellow('⏭️  跳过 Sass 迁移步骤'));
      return { success: true, skipped: true };
    }

    this.spinner = ora('步骤 3/8: 迁移 Sass/SCSS 文件...').start();

    try {
      const migrator = new SassMigrator(this.targetProjectPath, {
        verbose: this.options.verbose,
        dryRun: this.options.dryRun,
        autoFix: true,
        autoImportVariables: true
      });

      const result = await migrator.migrate();

      this.spinner.succeed('✅ Sass/SCSS 文件迁移完成');

      return {
        success: true,
        ...result,
        message: 'Sass/SCSS 文件迁移成功'
      };
    } catch (error) {
      this.spinner.fail('❌ Sass/SCSS 文件迁移失败');
      const errorMsg = `Sass 迁移失败: ${error.message}`;
      this.stats.errors.push(errorMsg);
      throw new Error(errorMsg);
    }
  }

  /**
   * 步骤 4: 执行 Vue 代码迁移
   */
  async migrateVueCode() {
    if (this.options.skipSteps.includes('vue')) {
      this.log(chalk.yellow('⏭️  跳过 Vue 代码迁移步骤'));
      return { success: true, skipped: true };
    }

    this.spinner = ora('步骤 4/8: 迁移 Vue 代码文件...').start();

    try {
      const migrator = new VueMigrator(this.targetProjectPath, {
        verbose: this.options.verbose,
        isOutputMode: false,
        srcDir: 'src',
        outputSrcDir: 'src',
        includePatterns: ['**/*.vue', '**/*.js', '**/*.ts'],
        excludePatterns: ['**/node_modules/**', '**/dist/**', '**/build/**', '**/.*/**']
      });

      const result = await migrator.migrate();

      this.spinner.succeed('✅ Vue 代码文件迁移完成');

      return {
        success: true,
        ...result,
        message: 'Vue 代码文件迁移成功'
      };
    } catch (error) {
      this.spinner.fail('❌ Vue 代码文件迁移失败');
      const errorMsg = `Vue 代码迁移失败: ${error.message}`;
      this.stats.errors.push(errorMsg);
      throw new Error(errorMsg);
    }
  }

  /**
   * 步骤 5: 安装依赖包
   */
  async installDependencies() {
    if (this.options.skipSteps.includes('install')) {
      this.log(chalk.yellow('⏭️  跳过依赖安装步骤'));
      return { success: true, skipped: true };
    }

    this.spinner = ora('步骤 5/8: 安装依赖包...').start();

    try {
      const { spawn } = require('child_process');
      const installCommand = this.options.buildFixerOptions?.installCommand || 'pnpm install';
      const [cmd, ...args] = installCommand.split(' ');

      this.log(chalk.gray(`   执行命令: ${installCommand}`));

      if (this.options.dryRun) {
        this.spinner.succeed('✅ 依赖安装完成 (预览模式)');
        return {
          success: true,
          message: '依赖安装完成 (预览模式)',
          command: installCommand
        };
      }

      // 执行安装命令
      const result = await new Promise((resolve, reject) => {
        const process = spawn(cmd, args, {
          cwd: this.targetProjectPath,
          stdio: this.options.verbose ? 'inherit' : 'pipe',
          shell: true
        });

        let output = '';
        let errorOutput = '';

        if (process.stdout) {
          process.stdout.on('data', (data) => {
            output += data.toString();
          });
        }

        if (process.stderr) {
          process.stderr.on('data', (data) => {
            errorOutput += data.toString();
          });
        }

        process.on('close', (code) => {
          if (code === 0) {
            resolve({
              success: true,
              output,
              command: installCommand
            });
          } else {
            reject(new Error(`安装命令失败 (退出码: ${code})\n${errorOutput}`));
          }
        });

        process.on('error', (error) => {
          reject(new Error(`执行安装命令时出错: ${error.message}`));
        });
      });

      this.spinner.succeed('✅ 依赖安装完成');

      return {
        success: true,
        ...result,
        message: '依赖安装成功'
      };
    } catch (error) {
      this.spinner.fail('❌ 依赖安装失败');
      const errorMsg = `依赖安装失败: ${error.message}`;
      this.stats.errors.push(errorMsg);
      throw new Error(errorMsg);
    }
  }

  /**
   * 步骤 6: 执行构建修复
   */
  async fixBuild() {
    if (this.options.skipSteps.includes('build')) {
      this.log(chalk.yellow('⏭️  跳过构建修复步骤'));
      return { success: true, skipped: true };
    }

    this.spinner = ora('步骤 6/8: 修复构建错误...').start();

    try {
      const buildFixer = new BuildFixer(this.targetProjectPath, {
        buildCommand: 'npm run build',
        devCommand: 'npm run dev',
        installCommand: 'npm install',
        maxAttempts: 6,
        mode: 'build',
        devTimeout: 60000,
        legacyPeerDeps: true,
        skipInstall: false,
        skipAI: false,
        verbose: this.options.verbose,
        dryRun: this.options.dryRun,
        ...this.options.buildFixerOptions
      });

      const result = await buildFixer.buildAndFix();

      if (result.success) {
        this.spinner.succeed('✅ 构建修复完成');
      } else {
        this.spinner.warn('⚠️  构建修复部分完成，可能需要手动处理');
      }

      return {
        success: result.success,
        ...result,
        message: result.success ? '构建修复成功' : '构建修复部分完成'
      };
    } catch (error) {
      this.spinner.fail('❌ 构建修复失败');
      const errorMsg = `构建修复失败: ${error.message}`;
      this.stats.errors.push(errorMsg);

      // 构建修复失败不应该阻止整个流程，返回失败结果但不抛出异常
      return {
        success: false,
        error: errorMsg,
        message: '构建修复失败'
      };
    }
  }

  /**
   * 步骤 7: 启动开发服务器（仅在构建成功时执行）
   */
  async startDevServer (buildResult) {
    if (this.options.skipSteps.includes('devserver')) {
      this.log(chalk.yellow('⏭️  跳过开发服务器启动步骤'))
      return { success: true, skipped: true }
    }

    // 只有在构建成功时才启动开发服务器
    if (!buildResult || !buildResult.success) {
      this.log(chalk.yellow('⏭️  由于构建未成功，跳过开发服务器启动步骤'))
      return {
        success: false,
        skipped: true,
        reason: '构建未成功',
        message: '由于构建未成功，跳过开发服务器启动'
      }
    }

    this.spinner = ora('步骤 7/8: 启动开发服务器...').start()

    const devRunFixer = new DevRunFixer(this.targetProjectPath, {
      verbose: this.options.verbose,
      dryRun: this.options.dryRun,
      devCommand: this.options.devCommand || 'npm run dev',
      timeout: 60000,
      maxAttempts: 10
    })

    const fixResult = await devRunFixer.fixDevServer()

    if (fixResult?.success) {
      this.spinner.succeed('✅ 开发服务器启动成功（经过修复）')

      return {
        success: true,
        port: this.options.port,
        baseUrl: fixResult.baseUrl,
        message: '开发服务器启动成功（经过修复）',
        fixStats: fixResult.stats
      }
    }

    this.spinner.fail('❌ 开发服务器启动失败（修复后仍失败）')
    return {
      success: false,
      error: fixResult.error || result.error || '开发服务器启动失败',
      message: '开发服务器启动失败',
      fixStats: fixResult.stats
    }
  }

  /**
   * 步骤 8: 执行页面验证（仅在开发服务器启动成功时执行）
   */
  async validatePages(devServerResult) {
    if (this.options.skipSteps.includes('validate')) {
      this.log(chalk.yellow('⏭️  跳过页面验证步骤'));
      return { success: true, skipped: true };
    }

    // 只有在开发服务器启动成功时才执行页面验证
    if (!devServerResult || !devServerResult.success) {
      this.log(chalk.yellow('⏭️  由于开发服务器未启动成功，跳过页面验证步骤'));
      return {
        success: false,
        skipped: true,
        reason: '开发服务器未启动成功',
        message: '由于开发服务器未启动成功，跳过页面验证'
      };
    }

    this.spinner = ora('步骤 8/8: 验证页面运行状态...').start();

    try {
      const validator = new RuntimePageChecker(this.targetProjectPath, {
        port: devServerResult.port || 3000,
        baseUrl: devServerResult.baseUrl,
        timeout: 60000,
        headless: true,
        autoFix: true,
        maxFixAttempts: 3,
        verbose: this.options.verbose,
        dryRun: this.options.dryRun,
        // 从 pageValidatorOptions 中获取用户名和密码，如果没有则使用默认值
        username: this.options.pageValidatorOptions?.username || 'admin',
        password: this.options.pageValidatorOptions?.password || '111111',
        ...this.options.pageValidatorOptions
      });

      const result = await validator.checkAllPages();

      if (result.success) {
        this.spinner.succeed('✅ 页面验证完成');
      } else {
        this.spinner.warn('⚠️  页面验证发现问题，请查看详细报告');
      }

      return {
        success: result.success,
        ...result,
        message: result.success ? '页面验证成功' : '页面验证发现问题'
      };
    } catch (error) {
      this.spinner.fail('❌ 页面验证失败');
      const errorMsg = `页面验证失败: ${error.message}`;
      this.stats.errors.push(errorMsg);

      // 页面验证失败不应该阻止整个流程，返回失败结果但不抛出异常
      return {
        success: false,
        error: errorMsg,
        message: '页面验证失败'
      };
    }
  }

  /**
   * 执行完整的自动迁移流程
   */
  async migrate() {
    console.log(chalk.blue('🚀 开始 Vue 2 到 Vue 3 全自动迁移...'));
    if (this.inPlaceMode) {
      console.log(chalk.gray(`   项目路径: ${this.projectPath} (就地迁移)`));
    } else {
      console.log(chalk.gray(`   源项目路径: ${this.projectPath}`));
      console.log(chalk.gray(`   目标项目路径: ${this.targetProjectPath}`));
    }

    if (this.options.dryRun) {
      console.log(chalk.yellow('   🔍 预览模式：不会实际修改文件'));
    }

    this.stats.startTime = Date.now();

    try {
      // 验证项目路径
      await this.validatePaths();

      // 步骤 1: 复制配置文件
      this.stats.stepResults.configCopy = await this.copyVueConfig();

      // 步骤 2: 迁移 package.json
      this.stats.stepResults.packageMigration = await this.migratePackageJson();

      // 步骤 3: 迁移 Sass
      this.stats.stepResults.sassMigration = await this.migrateSass();

      // 步骤 4: 迁移 Vue 代码
      this.stats.stepResults.vueMigration = await this.migrateVueCode();

      // 步骤 5: 安装依赖包
      this.stats.stepResults.dependencyInstall = await this.installDependencies();

      // 步骤 6: 修复构建
      this.stats.stepResults.buildFix = await this.fixBuild();

      // 步骤 7: 启动开发服务器（仅在构建成功时）
      this.stats.stepResults.devServerStart = await this.startDevServer(this.stats.stepResults.buildFix);

      // 步骤 8: 验证页面（仅在开发服务器启动成功时）
      this.stats.stepResults.pageValidation = await this.validatePages(this.stats.stepResults.devServerStart);

      this.stats.success = true;
      this.stats.endTime = Date.now();

      // 打印最终报告
      this.printFinalReport();

      return this.stats;
    } catch (error) {
      this.stats.success = false;
      this.stats.endTime = Date.now();

      console.error(chalk.red(`\n❌ 迁移失败: ${error.message}`));

      if (this.options.verbose) {
        console.error(chalk.gray('\n详细错误信息:'));
        console.error(chalk.gray(error.stack));
      }

      // 打印部分完成的报告
      this.printFinalReport();

      throw error;
    }
  }

  /**
   * 验证项目路径
   */
  async validatePaths() {
    // 验证项目路径
    if (!await fs.pathExists(this.projectPath)) {
      throw new Error(`项目路径不存在: ${this.projectPath}`);
    }

    // 验证项目是否为 Vue 项目
    const packageJsonPath = path.join(this.projectPath, 'package.json');
    if (!await fs.pathExists(packageJsonPath)) {
      throw new Error(`项目缺少 package.json 文件: ${packageJsonPath}`);
    }

    // 如果不是就地模式，确保目标目录存在
    if (!this.inPlaceMode) {
      await fs.ensureDir(this.targetProjectPath);
    }

    this.log(chalk.green('✅ 项目路径验证通过'));
  }

  /**
   * 打印最终报告
   */
  printFinalReport() {
    const duration = this.stats.endTime - this.stats.startTime;
    const durationStr = `${(duration / 1000).toFixed(2)}s`;

    console.log(chalk.blue('\n📊 迁移报告'));
    console.log(chalk.gray('=' .repeat(50)));

    console.log(chalk.gray(`⏱️  总耗时: ${durationStr}`));
    console.log(chalk.gray(`📁 源项目: ${this.sourceProjectPath}`));
    console.log(chalk.gray(`📁 目标项目: ${this.targetProjectPath}`));

    // 打印各步骤结果
    const steps = [
      { key: 'configCopy', name: '配置文件复制', icon: '⚙️' },
      { key: 'packageMigration', name: 'package.json 迁移', icon: '📦' },
      { key: 'sassMigration', name: 'Sass 迁移', icon: '🎨' },
      { key: 'vueMigration', name: 'Vue 代码迁移', icon: '🔄' },
      { key: 'dependencyInstall', name: '依赖安装', icon: '📥' },
      { key: 'buildFix', name: '构建修复', icon: '🔧' },
      { key: 'devServerStart', name: '开发服务器启动', icon: '🚀' },
      { key: 'pageValidation', name: '页面验证', icon: '✅' }
    ];

    console.log(chalk.blue('\n📋 步骤执行结果:'));

    steps.forEach((step, index) => {
      const result = this.stats.stepResults[step.key];
      const stepNum = `${index + 1}/8`;

      if (!result) {
        console.log(chalk.gray(`   ${stepNum} ${step.icon} ${step.name}: 未执行`));
      } else if (result.skipped) {
        console.log(chalk.yellow(`   ${stepNum} ${step.icon} ${step.name}: 已跳过 ${result.reason ? `(${result.reason})` : ''}`));
      } else if (result.success) {
        console.log(chalk.green(`   ${stepNum} ${step.icon} ${step.name}: 成功`));
      } else {
        console.log(chalk.red(`   ${stepNum} ${step.icon} ${step.name}: 失败`));
      }
    });

    // 打印错误信息
    if (this.stats.errors.length > 0) {
      console.log(chalk.red('\n❌ 错误信息:'));
      this.stats.errors.forEach((error, index) => {
        console.log(chalk.red(`   ${index + 1}. ${error}`));
      });
    }

    // 打印最终状态
    if (this.stats.success) {
      console.log(chalk.green('\n🎉 迁移完成！'));
      console.log(chalk.gray('   建议运行以下命令验证迁移结果:'));
      console.log(chalk.gray(`   cd ${this.targetProjectPath}`));
      console.log(chalk.gray('   npm install'));
      console.log(chalk.gray('   npm run build'));
      console.log(chalk.gray('   npm run dev'));
    } else {
      console.log(chalk.red('\n💥 迁移未完全成功'));
      console.log(chalk.gray('   请查看上述错误信息并手动修复相关问题'));
    }
  }

  /**
   * 清理资源
   */
  async cleanup() {
    if (this.devServerManager) {
      try {
        await this.devServerManager.stopDevServer();
        this.log(chalk.gray('✅ 开发服务器已停止'));
      } catch (error) {
        this.log(chalk.yellow(`⚠️  停止开发服务器时出错: ${error.message}`));
      }
    }
  }
}

module.exports = AutoMigrator;
