// 由于 MCP SDK 可能还没有安装，我们先创建一个模拟的 MCP 服务器
// 在实际使用时，需要安装 @modelcontextprotocol/sdk
// const { McpServer, ResourceTemplate } = require("@modelcontextprotocol/sdk/server/mcp.js");
// const { StdioServerTransport } = require("@modelcontextprotocol/sdk/server/stdio.js");

const { z } = require("zod");
const path = require('path');
const fs = require('fs-extra');
const chalk = require('chalk');

const BuildFixAgent = require('../domain/build-fix/BuildFixAgent');

// 模拟 MCP 服务器类 - 用于测试和开发
class MockMcpServer {
  constructor(config) {
    this.name = config.name;
    this.version = config.version;
    this.tools = new Map();
    this.resources = new Map();
  }

  registerTool(name, schema, handler) {
    this.tools.set(name, { name, schema, handler });
  }

  registerResource(name, uri, schema, handler) {
    this.resources.set(name, { name, uri, schema, handler });
  }

  listTools() {
    return Array.from(this.tools.values()).map(tool => ({
      name: tool.name,
      ...tool.schema
    }));
  }

  listResources() {
    return Array.from(this.resources.values()).map(resource => ({
      name: resource.name,
      uri: resource.uri,
      ...resource.schema
    }));
  }

  async connect(transport) {
    // 模拟连接
    console.log(`Mock MCP Server ${this.name} connected`);
  }
}

class MockStdioServerTransport {
  // 模拟 stdio 传输
}

// 模拟 ResourceTemplate
class MockResourceTemplate {
  constructor(template, options) {
    this.template = template;
    this.options = options;
  }
}

/**
 * BuildFixMcpServer - MCP 服务器封装 BuildFixAgent
 * 
 * 提供符合 MCP 原则的简洁接口：
 * - 工具：fix-build-errors, fix-runtime-error
 * - 资源：fix-stats, session-history, fix-logs
 * 
 * 设计原则：
 * - 参数少且简洁
 * - 资源导向的数据暴露
 * - 工具导向的操作执行
 */
class BuildFixMcpServer {
  constructor(options = {}) {
    this.options = {
      verbose: options.verbose || false,
      ...options
    };

    // 创建 MCP 服务器 (使用模拟版本进行测试)
    this.server = new MockMcpServer({
      name: "build-fix-agent",
      version: "1.0.0"
    });

    // BuildFixAgent 实例缓存
    this.agents = new Map(); // projectPath -> BuildFixAgent
    this.sessionStats = new Map(); // sessionId -> stats
    this.currentSessionId = null;

    this.setupTools();
    this.setupResources();
  }

  /**
   * 设置 MCP 工具
   */
  setupTools() {
    // 工具1: 修复构建错误
    this.server.registerTool(
      "fix-build-errors",
      {
        title: "Fix Build Errors",
        description: "Automatically fix build errors in a project",
        inputSchema: {
          projectPath: z.string().describe("Project directory path"),
          buildOutput: z.string().describe("Build error output"),
          maxAttempts: z.number().optional().default(3).describe("Maximum fix attempts")
        }
      },
      async ({ projectPath, buildOutput, maxAttempts = 3 }) => {
        try {
          const agent = this.getOrCreateAgent(projectPath);
          this.currentSessionId = this.generateSessionId();
          
          if (this.options.verbose) {
            console.log(chalk.blue(`🔧 Starting build fix for: ${projectPath}`));
          }

          // 解析构建输出中的文件列表
          const files = this.extractFilesFromBuildOutput(buildOutput);
          
          if (files.length === 0) {
            return {
              content: [{
                type: "text",
                text: "No files found in build output to fix"
              }]
            };
          }

          // 执行修复
          const result = await agent.fixFirstFile(files, buildOutput, 1);
          
          // 记录统计信息
          this.sessionStats.set(this.currentSessionId, {
            projectPath,
            type: 'build-fix',
            timestamp: new Date().toISOString(),
            result,
            files: files.length
          });

          return {
            content: [{
              type: "text",
              text: JSON.stringify({
                success: result.success,
                filesModified: result.filesModified,
                totalFiles: result.totalFiles,
                sessionId: this.currentSessionId,
                errors: result.errors
              }, null, 2)
            }]
          };
        } catch (error) {
          return {
            content: [{
              type: "text",
              text: `Error: ${error.message}`
            }],
            isError: true
          };
        }
      }
    );

    // 工具2: 修复运行时错误
    this.server.registerTool(
      "fix-runtime-error",
      {
        title: "Fix Runtime Error",
        description: "Fix runtime errors in Vue components",
        inputSchema: {
          projectPath: z.string().describe("Project directory path"),
          fileName: z.string().describe("File with runtime error"),
          errorMessage: z.string().describe("Runtime error message"),
          stack: z.string().optional().describe("Error stack trace")
        }
      },
      async ({ projectPath, fileName, errorMessage, stack }) => {
        try {
          const agent = this.getOrCreateAgent(projectPath);
          this.currentSessionId = this.generateSessionId();

          if (this.options.verbose) {
            console.log(chalk.blue(`🔧 Starting runtime fix for: ${fileName}`));
          }

          const errorContext = {
            fileName,
            message: errorMessage,
            stack: stack || '',
            type: 'runtime'
          };

          const result = await agent.fixRuntimeError(errorContext);

          // 记录统计信息
          this.sessionStats.set(this.currentSessionId, {
            projectPath,
            type: 'runtime-fix',
            timestamp: new Date().toISOString(),
            result,
            fileName
          });

          return {
            content: [{
              type: "text",
              text: JSON.stringify({
                success: result.success,
                fileName,
                sessionId: this.currentSessionId,
                hasNewContent: !!result.newContent,
                error: result.error
              }, null, 2)
            }]
          };
        } catch (error) {
          return {
            content: [{
              type: "text",
              text: `Error: ${error.message}`
            }],
            isError: true
          };
        }
      }
    );
  }

  /**
   * 设置 MCP 资源
   */
  setupResources() {
    // 资源1: 修复统计信息
    this.server.registerResource(
      "fix-stats",
      "fix-stats://current",
      {
        title: "Fix Statistics",
        description: "Current session fix statistics",
        mimeType: "application/json"
      },
      async (uri) => {
        const stats = this.currentSessionId ? 
          this.sessionStats.get(this.currentSessionId) : null;
        
        const content = stats ? {
          sessionId: this.currentSessionId,
          ...stats
        } : {
          message: "No active session"
        };

        return {
          contents: [{
            uri: uri.href,
            text: JSON.stringify(content, null, 2),
            mimeType: "application/json"
          }]
        };
      }
    );

    // 资源2: 会话历史
    this.server.registerResource(
      "session-history",
      "session-history://latest",
      {
        title: "Session History",
        description: "Latest session history and logs",
        mimeType: "application/json"
      },
      async (uri) => {
        const allSessions = Array.from(this.sessionStats.entries()).map(([id, stats]) => ({
          sessionId: id,
          ...stats
        }));

        return {
          contents: [{
            uri: uri.href,
            text: JSON.stringify({
              totalSessions: allSessions.length,
              currentSession: this.currentSessionId,
              sessions: allSessions.slice(-10) // 最近10个会话
            }, null, 2),
            mimeType: "application/json"
          }]
        };
      }
    );

    // 资源3: 特定会话的修复日志
    this.server.registerResource(
      "fix-logs",
      new MockResourceTemplate("fix-logs://{sessionId}", { list: undefined }),
      {
        title: "Fix Logs",
        description: "Detailed logs for a specific session"
      },
      async (uri, { sessionId }) => {
        const stats = this.sessionStats.get(sessionId);
        
        if (!stats) {
          return {
            contents: [{
              uri: uri.href,
              text: JSON.stringify({
                error: `Session ${sessionId} not found`
              }, null, 2),
              mimeType: "application/json"
            }]
          };
        }

        // 尝试读取对应的日志文件
        const agent = this.agents.get(stats.projectPath);
        let logContent = "No detailed logs available";
        
        if (agent) {
          try {
            const logFiles = await agent.getSessionLogFiles();
            if (logFiles.length > 0) {
              // 读取最新的日志文件
              const latestLog = logFiles[logFiles.length - 1];
              if (await fs.pathExists(latestLog)) {
                logContent = await fs.readFile(latestLog, 'utf8');
              }
            }
          } catch (error) {
            logContent = `Error reading logs: ${error.message}`;
          }
        }

        return {
          contents: [{
            uri: uri.href,
            text: JSON.stringify({
              sessionId,
              ...stats,
              detailedLogs: logContent
            }, null, 2),
            mimeType: "application/json"
          }]
        };
      }
    );
  }

  /**
   * 获取或创建 BuildFixAgent 实例
   */
  getOrCreateAgent(projectPath) {
    const absolutePath = path.resolve(projectPath);
    
    if (!this.agents.has(absolutePath)) {
      const agent = new BuildFixAgent(absolutePath, {
        verbose: this.options.verbose,
        maxAttempts: 5
      });
      this.agents.set(absolutePath, agent);
    }
    
    return this.agents.get(absolutePath);
  }

  /**
   * 从构建输出中提取文件列表
   */
  extractFilesFromBuildOutput(buildOutput) {
    const files = [];
    const lines = buildOutput.split('\n');
    
    for (const line of lines) {
      // 匹配常见的错误文件路径模式
      const patterns = [
        /ERROR in (.+\.(?:vue|js|ts|jsx|tsx))/,
        /(.+\.(?:vue|js|ts|jsx|tsx)):\d+:\d+/,
        /Module not found.*'(.+\.(?:vue|js|ts|jsx|tsx))'/,
        /Failed to compile.*\n.*(.+\.(?:vue|js|ts|jsx|tsx))/
      ];
      
      for (const pattern of patterns) {
        const match = line.match(pattern);
        if (match && match[1]) {
          const filePath = match[1].trim();
          if (!files.includes(filePath)) {
            files.push(filePath);
          }
        }
      }
    }
    
    return files;
  }

  /**
   * 生成会话ID
   */
  generateSessionId() {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 启动 MCP 服务器
   */
  async start() {
    const transport = new MockStdioServerTransport();
    await this.server.connect(transport);

    if (this.options.verbose) {
      console.log(chalk.green("🚀 BuildFix MCP Server started"));
    }
  }
}

module.exports = BuildFixMcpServer;
