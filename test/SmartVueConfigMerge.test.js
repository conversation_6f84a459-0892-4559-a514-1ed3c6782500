const fs = require('fs-extra');
const path = require('path');
const SmartVueConfigMerge = require('../src/app/SmartVueConfigMerge');

describe('SmartVueConfigMerge', () => {
  let merger;
  let tempDir;

  beforeEach(async () => {
    // 创建临时目录
    tempDir = path.join(__dirname, 'temp', `test-${Date.now()}`);
    await fs.ensureDir(tempDir);

    // 初始化合并器
    merger = new SmartVueConfigMerge({
      verbose: false,
      dryRun: false
    });
  });

  afterEach(async () => {
    // 清理临时目录
    if (await fs.pathExists(tempDir)) {
      await fs.remove(tempDir);
    }
  });

  describe('mergeVueConfig', () => {
    test('应该在没有老配置时复制模板配置', async () => {
      const projectPath = path.join(tempDir, 'project');
      const targetPath = path.join(tempDir, 'target');

      await fs.ensureDir(projectPath);
      await fs.ensureDir(targetPath);

      const result = await merger.mergeVueConfig(projectPath, targetPath);

      expect(result.success).toBe(true);
      expect(result.action).toBe('template_copy');
      expect(result.aiUsed).toBe(false);

      // 验证目标文件存在
      const targetConfigPath = path.join(targetPath, 'vue.config.js');
      expect(await fs.pathExists(targetConfigPath)).toBe(true);
    });

    test('应该处理空的老配置文件', async () => {
      const projectPath = path.join(tempDir, 'project');
      const targetPath = path.join(tempDir, 'target');

      await fs.ensureDir(projectPath);
      await fs.ensureDir(targetPath);

      // 创建空的 vue.config.js
      await fs.writeFile(path.join(projectPath, 'vue.config.js'), '');

      const result = await merger.mergeVueConfig(projectPath, targetPath);

      expect(result.success).toBe(true);
      expect(result.action).toBe('template_copy');
      expect(result.aiUsed).toBe(false);
    });
  });

  describe('removeBabelConfig', () => {
    test('应该删除存在的 babel.config.js', async () => {
      const targetPath = path.join(tempDir, 'target');
      await fs.ensureDir(targetPath);

      // 创建 babel.config.js
      const babelConfigPath = path.join(targetPath, 'babel.config.js');
      await fs.writeFile(babelConfigPath, 'module.exports = {};');

      const removed = await merger.removeBabelConfig(targetPath);

      expect(removed).toBe(true);
      expect(await fs.pathExists(babelConfigPath)).toBe(false);
    });

    test('应该在文件不存在时返回 false', async () => {
      const targetPath = path.join(tempDir, 'target');
      await fs.ensureDir(targetPath);

      const removed = await merger.removeBabelConfig(targetPath);

      expect(removed).toBe(false);
    });
  });

  describe('buildMergePrompt', () => {
    test('应该生成正确的 AI 提示词', () => {
      const oldConfig = 'old config content';
      const templateConfig = 'template config content';

      const prompt = merger.buildMergePrompt(oldConfig, templateConfig);

      expect(prompt).toContain('Vue.js 迁移专家');
      expect(prompt).toContain('Vue 2 + Webpack 4');
      expect(prompt).toContain('Vue 3 + Webpack 5');
      expect(prompt).toContain(oldConfig);
      expect(prompt).toContain(templateConfig);
      expect(prompt).toContain('```javascript');
    });
  });

  describe('dryRun 模式', () => {
    test('应该在 dryRun 模式下不修改文件', async () => {
      const dryRunMerger = new SmartVueConfigMerge({
        verbose: false,
        dryRun: true
      });

      const projectPath = path.join(tempDir, 'project');
      const targetPath = path.join(tempDir, 'target');

      await fs.ensureDir(projectPath);
      await fs.ensureDir(targetPath);

      const result = await dryRunMerger.mergeVueConfig(projectPath, targetPath);

      expect(result.success).toBe(true);

      // 验证目标文件不存在（因为是 dryRun）
      const targetConfigPath = path.join(targetPath, 'vue.config.js');
      expect(await fs.pathExists(targetConfigPath)).toBe(false);
    });
  });
});
