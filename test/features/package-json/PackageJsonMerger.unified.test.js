const fs = require('fs-extra');
const path = require('path');
const PackageJsonMerger = require('../../../src/features/package-json/PackageJsonMerger');

describe('PackageJsonMerger - 统一功能测试', () => {
  const testFixturesDir = path.join(__dirname, '../../fixtures');
  const tempDir = path.join(__dirname, '../../temp');

  beforeEach(async () => {
    await fs.ensureDir(tempDir);
  });

  afterEach(async () => {
    await fs.remove(tempDir);
  });

  describe('合并模式', () => {
    test('应该能够智能合并两个项目的 package.json', async () => {
      // 准备源项目
      const sourceProjectPath = path.join(tempDir, 'source-project');
      await fs.ensureDir(sourceProjectPath);

      const sourcePackageJson = {
        name: 'source-project',
        version: '1.0.0',
        dependencies: {
          vue: '^2.6.14',
          axios: '^0.27.2'
        }
      };

      await fs.writeJson(path.join(sourceProjectPath, 'package.json'), sourcePackageJson, { spaces: 2 });

      // 准备目标项目
      const targetProjectPath = path.join(tempDir, 'target-project');
      await fs.ensureDir(targetProjectPath);

      const targetPackageJson = {
        name: 'target-project',
        version: '2.0.0',
        dependencies: {
          vue: '^3.4.0',
          'vue-router': '^4.5.0'
        }
      };

      await fs.writeJson(path.join(targetProjectPath, 'package.json'), targetPackageJson, { spaces: 2 });

      // 创建合并器
      const merger = PackageJsonMerger.createMerger(sourceProjectPath, targetProjectPath, {
        preserveTargetDependencies: true,
        verbose: true,
        dryRun: false
      });

      // 执行合并
      const result = await merger.process();

      // 验证结果
      expect(result.success).toBe(true);
      expect(result.changes).toBeDefined();

      // 验证合并后的文件
      const mergedPackageJson = await fs.readJson(path.join(targetProjectPath, 'package.json'));

      // 目标项目的基本信息应该被保留
      expect(mergedPackageJson.name).toBe('target-project');
      expect(mergedPackageJson.version).toBe('2.0.0');

      // 目标项目的 Vue 3 依赖应该被保留
      expect(mergedPackageJson.dependencies.vue).toBe('^3.4.0');
      expect(mergedPackageJson.dependencies['vue-router']).toBe('^4.5.0');

      // 源项目的兼容依赖应该被添加
      expect(mergedPackageJson.dependencies.axios).toBe('^0.27.2');
    });

    test('应该使用静态工厂方法创建合并器', () => {
      const merger = PackageJsonMerger.createMerger('/source', '/target', { verbose: true });

      expect(merger.mode).toBe('merge');
      expect(merger.sourceProjectPath).toBe('/source');
      expect(merger.targetProjectPath).toBe('/target');
      expect(merger.options.verbose).toBe(true);
    });
  });

  describe('升级模式', () => {
    test('应该能够升级 Vue 2 项目的依赖到 Vue 3', async () => {
      // 准备测试数据
      const testProjectPath = path.join(tempDir, 'test-project');
      await fs.ensureDir(testProjectPath);

      const vue2PackageJson = {
        name: 'test-vue2-project',
        version: '1.0.0',
        dependencies: {
          vue: '^2.6.14',
          'vue-router': '^3.5.4',
          vuex: '^3.6.2'
        },
        devDependencies: {
          'vue-template-compiler': '^2.6.14'
        }
      };

      await fs.writeJson(path.join(testProjectPath, 'package.json'), vue2PackageJson, { spaces: 2 });

      // 创建升级器
      const upgrader = PackageJsonMerger.createUpgrader(testProjectPath, {
        migrationMode: true,
        verbose: true,
        dryRun: false
      });

      // 执行升级
      const result = await upgrader.process();

      // 验证结果
      expect(result.success).toBe(true);
      expect(result.upgraded.length).toBeGreaterThan(0);
      expect(result.added.length).toBeGreaterThan(0);

      // 验证升级后的 package.json
      const upgradedPackageJson = await fs.readJson(path.join(testProjectPath, 'package.json'));

      // Vue 3 依赖应该被升级
      expect(upgradedPackageJson.dependencies.vue).toMatch(/^\^3\./);
      expect(upgradedPackageJson.dependencies['vue-router']).toMatch(/^\^4\./);
      expect(upgradedPackageJson.dependencies.vuex).toMatch(/^\^4\./);

      // 不兼容的依赖应该被移除
      expect(upgradedPackageJson.devDependencies?.['vue-template-compiler']).toBeUndefined();

      // 新的依赖应该被添加
      expect(upgradedPackageJson.dependencies['@vue/compiler-sfc']).toBeDefined();
    });

    test('应该使用静态工厂方法创建升级器', () => {
      const upgrader = PackageJsonMerger.createUpgrader('/working', { migrationMode: true });

      expect(upgrader.mode).toBe('upgrade');
      expect(upgrader.projectPath).toBe('/working');
      expect(upgrader.options.migrationMode).toBe(true);
    });

    test('应该正确处理 needsUpgrade 依赖', async () => {
      // 准备测试数据
      const testProjectPath = path.join(tempDir, 'test-needsupgrade');
      await fs.ensureDir(testProjectPath);

      const packageJsonWithNeedsUpgrade = {
        name: 'test-needsupgrade-project',
        version: '1.0.0',
        dependencies: {
          vue: '^2.6.14',
          'vue-count-to': '^1.0.10' // 这个在 needsUpgrade 列表中
        }
      };

      await fs.writeJson(path.join(testProjectPath, 'package.json'), packageJsonWithNeedsUpgrade, { spaces: 2 });

      // 创建升级器
      const upgrader = PackageJsonMerger.createUpgrader(testProjectPath, {
        migrationMode: true,
        verbose: false,
        dryRun: false
      });

      // 执行升级
      const result = await upgrader.process();

      // 验证结果
      expect(result.success).toBe(true);
      expect(result.upgraded.length).toBeGreaterThan(0);

      // 验证升级后的 package.json
      const upgradedPackageJson = await fs.readJson(path.join(testProjectPath, 'package.json'));

      // vue-count-to 应该被升级到 needsUpgrade 中指定的版本
      expect(upgradedPackageJson.dependencies['vue-count-to']).toBe('^1.0.10');
    });
  });

  describe('构造函数模式检测', () => {
    test('应该正确检测合并模式', () => {
      const merger = new PackageJsonMerger('/source', '/target', { verbose: true });

      expect(merger.mode).toBe('merge');
      expect(merger.sourceProjectPath).toBe('/source');
      expect(merger.targetProjectPath).toBe('/target');
    });

    test('应该正确检测升级模式', () => {
      const upgrader = new PackageJsonMerger({ projectPath: '/working', migrationMode: true });

      expect(upgrader.mode).toBe('upgrade');
      expect(upgrader.projectPath).toBe('/working');
      expect(upgrader.options.migrationMode).toBe(true);
    });
  });

  describe('错误处理', () => {
    test('合并模式应该处理缺失的源文件', async () => {
      const sourceProjectPath = path.join(tempDir, 'missing-source');
      const targetProjectPath = path.join(tempDir, 'target-project');

      // 只创建目标项目
      await fs.ensureDir(targetProjectPath);
      await fs.writeJson(path.join(targetProjectPath, 'package.json'), { name: 'target' }, { spaces: 2 });

      const merger = PackageJsonMerger.createMerger(sourceProjectPath, targetProjectPath);

      await expect(merger.process()).rejects.toThrow('源项目中未找到 package.json');
    });

    test('升级模式应该处理缺失的 package.json', async () => {
      const testProjectPath = path.join(tempDir, 'missing-package');
      await fs.ensureDir(testProjectPath);

      const upgrader = PackageJsonMerger.createUpgrader(testProjectPath);

      await expect(upgrader.process()).rejects.toThrow('package.json 不存在');
    });
  });
});
