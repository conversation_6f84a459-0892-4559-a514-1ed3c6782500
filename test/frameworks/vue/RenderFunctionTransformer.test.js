const RenderFunctionTransformer = require('../../../src/frameworks/vue/RenderFunctionTransformer');

describe('RenderFunctionTransformer - Functional Component', () => {
  let transformer;

  beforeEach(() => {
    transformer = new RenderFunctionTransformer({
      verbose: false
    });
  });

  describe('isFunctionalComponent', () => {
    it('should detect functional component with render method', () => {
      const code = `
        export default {
          functional: true,
          props: {
            icon: String,
            title: String
          },
          render(h, context) {
            return h('div', 'test')
          }
        }
      `;
      expect(transformer.isFunctionalComponent(code)).toBe(true);
    });

    it('should detect functional component with destructured context', () => {
      const code = `
        export default {
          functional: true,
          props: {
            icon: String,
            title: String
          },
          render(h, { props, slots }) {
            return h('div', 'test')
          }
        }
      `;
      expect(transformer.isFunctionalComponent(code)).toBe(true);
    });

    it('should not detect non-functional component', () => {
      const code = `
        export default {
          props: {
            icon: String,
            title: String
          },
          render(h) {
            return h('div', 'test')
          }
        }
      `;
      expect(transformer.isFunctionalComponent(code)).toBe(false);
    });
  });

  describe('transformFunctionalComponent', () => {
    it('should transform simple functional component', async () => {
      const input = `<script>
export default {
  name: 'MenuItem',
  functional: true,
  props: {
    icon: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    }
  },
  render(h, context) {
    const { icon, title } = context.props
    const vnodes = []

    if (icon) {
      vnodes.push(<svg-icon icon-class={icon}/>)
    }

    if (title) {
      vnodes.push(<span slot='title'>{title}</span>)
    }
    return vnodes
  }
}
</script>`;

      const result = await transformer.transform(input, 'Item.vue');

      // 验证转换结果
      expect(result).not.toContain('functional: true');
      expect(result).toContain('setup(props, { slots, emit, attrs })');
      expect(result).not.toContain('context.props');
      expect(result).toContain('const { icon, title } = props');
    });

    it('should transform functional component with JSX to h() calls', async () => {
      const input = `<script>
export default {
  functional: true,
  props: {
    text: String
  },
  render(h, context) {
    return <div class="wrapper">{context.props.text}</div>
  }
}
</script>`;

      const result = await transformer.transform(input, 'Component.vue');

      // 验证转换结果
      expect(result).not.toContain('functional: true');
      expect(result).toContain('setup(props, { slots, emit, attrs })');
      expect(result).not.toContain('context.props');
      expect(result).toContain('props.text');

      // 验证 JSX 转换为 h() 函数调用
      expect(result).toMatch(/h\s*\(\s*['"']div['"]/);
    });

    it('should handle functional component with multiple return statements', async () => {
      const input = `<script>
export default {
  functional: true,
  props: {
    condition: Boolean,
    text: String
  },
  render(h, context) {
    if (context.props.condition) {
      return <div>{context.props.text}</div>
    }
    return <span>default</span>
  }
}
</script>`;

      const result = await transformer.transform(input, 'ConditionalComponent.vue');

      // 验证转换结果
      expect(result).not.toContain('functional: true');
      expect(result).toContain('setup(props, { slots, emit, attrs })');
      expect(result).not.toContain('context.props');
      expect(result).toContain('props.condition');
      expect(result).toContain('props.text');
    });

    it('should handle functional component with function expression syntax', async () => {
      const input = `<script>
export default {
  functional: true,
  props: {
    text: String
  },
  render: function(h, context) {
    return h('div', context.props.text)
  }
}
</script>`;

      const result = await transformer.transform(input, 'FunctionComponent.vue');

      expect(result).toContain('props.text');
    });
  });

  describe('transformVariableDeclaration', () => {
    it('should transform context.props to props', () => {
      const stmt = {
        type: 'VariableDeclaration',
        declarations: [{
          type: 'VariableDeclarator',
          id: { type: 'ObjectPattern' },
          init: { type: 'MemberExpression' }
        }]
      };

      // 模拟生成的代码
      jest.spyOn(transformer, 'transformVariableDeclaration').mockImplementation(() => {
        return 'const { icon, title } = props';
      });

      const result = transformer.transformVariableDeclaration(stmt);
      expect(result).toBe('const { icon, title } = props');
    });
  });

  describe('transformReturnStatement', () => {
    it('should transform context references in return statement', () => {
      const stmt = {
        type: 'ReturnStatement',
        argument: { type: 'JSXElement' }
      };

      // 模拟生成的代码
      jest.spyOn(transformer, 'transformReturnStatement').mockImplementation(() => {
        return 'return props.text';
      });

      const result = transformer.transformReturnStatement(stmt);
      expect(result).toBe('return props.text');
    });
  });

  describe('error handling', () => {
    it('should handle parsing errors gracefully', async () => {
      const invalidInput = `<script>
export default {
  functional: true,
  render(h, context) {
    return <div>{unclosed
  }
}
</script>`;

      const result = await transformer.transform(invalidInput, 'Invalid.vue');

      // 应该返回原始代码，不抛出异常
      expect(result).toBe(invalidInput);
    });

    it('should handle missing script content', async () => {
      const noScriptInput = `<template>
  <div>No script</div>
</template>`;

      const result = await transformer.transform(noScriptInput, 'NoScript.vue');

      // 应该返回原始代码
      expect(result).toBe(noScriptInput);
    });
  });
});
